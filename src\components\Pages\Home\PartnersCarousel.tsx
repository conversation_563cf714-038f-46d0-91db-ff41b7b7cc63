'use client';

import React from 'react';
import Image from 'next/image';

interface Partner {
  name: string;
  logo: string;
  type: 'press' | 'partner' | 'exchange';
}

const partners: Partner[] = [
  { name: 'Binance', logo: '/logos/binance.svg', type: 'exchange' },
  { name: 'CoinDesk', logo: '/logos/coindesk.svg', type: 'press' },
  { name: 'Cointelegraph', logo: '/logos/cointelegraph.svg', type: 'press' },
  { name: 'Forbes', logo: '/logos/forbes.svg', type: 'press' },
  { name: 'Bloomberg', logo: '/logos/bloomberg.svg', type: 'press' },
  { name: 'Yahoo Finance', logo: '/logos/yahoo-finance.svg', type: 'press' },
  { name: 'NASDAQ', logo: '/logos/nasdaq.svg', type: 'press' },
  { name: 'CNBC', logo: '/logos/cnbc.svg', type: 'press' },
  { name: 'Coinbase', logo: '/logos/coinbase.svg', type: 'exchange' },
  { name: 'Kraken', logo: '/logos/kraken.svg', type: 'exchange' },
  { name: 'TechCrunch', logo: '/logos/techcrunch.svg', type: 'press' },
  { name: 'The Block', logo: '/logos/theblock.svg', type: 'press' },
];

const PartnersCarousel: React.FC = () => {
  return (
    <section className="py-16 bg-white border-t border-gray-100">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h3 className="text-xl text-gray-600 mb-2">Featured In & Trusted By</h3>
          <p className="text-gray-500">Leading cryptocurrency exchanges and financial media</p>
        </div>
        
        {/* Carousel Container */}
        <div className="relative overflow-hidden">
          {/* Gradient fade on edges */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10 pointer-events-none"></div>
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10 pointer-events-none"></div>
          
          {/* Scrolling logos */}
          <div className="flex items-center justify-center">
            <div className="flex space-x-16 animate-marquee">
              {/* First set of logos */}
              {partners.map((partner, index) => (
                <div 
                  key={`first-${index}`}
                  className="flex-shrink-0 flex items-center justify-center h-16 w-32"
                >
                  <Image 
                    src={partner.logo} 
                    alt={`${partner.name} logo`} 
                    width={120}
                    height={40}
                    className="h-8 w-auto grayscale opacity-60 hover:grayscale-0 hover:opacity-100 transition-all duration-300 object-contain"
                  />
                </div>
              ))}
              
              {/* Duplicate set for seamless loop */}
              {partners.map((partner, index) => (
                <div 
                  key={`second-${index}`}
                  className="flex-shrink-0 flex items-center justify-center h-16 w-32"
                >
                  <Image 
                    src={partner.logo} 
                    alt={`${partner.name} logo`} 
                    width={120}
                    height={40}
                    className="h-8 w-auto grayscale opacity-60 hover:grayscale-0 hover:opacity-100 transition-all duration-300 object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Trust Indicators */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="flex flex-col items-center">
            <div className="text-2xl font-bold text-primary mb-2">500K+</div>
            <div className="text-gray-600 text-sm">Social Media Followers</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-2xl font-bold text-primary mb-2">50+</div>
            <div className="text-gray-600 text-sm">Media Mentions</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-2xl font-bold text-primary mb-2">15+</div>
            <div className="text-gray-600 text-sm">Exchange Partnerships</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="text-2xl font-bold text-primary mb-2">100+</div>
            <div className="text-gray-600 text-sm">Countries Reached</div>
          </div>
        </div>
      </div>
      
      {/* Custom CSS for marquee animation */}
      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(0%);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-marquee {
          animation: marquee 30s linear infinite;
        }
        
        .animate-marquee:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  );
};

export default PartnersCarousel;

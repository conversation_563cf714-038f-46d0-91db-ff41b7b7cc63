// Enhanced analytics tracking utilities

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    fbq: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Generic event tracking
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
  
  // Also push to dataLayer for GTM
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event: eventName,
      ...parameters
    });
  }
};

// E-commerce tracking
export const trackPurchase = (transactionId: string, value: number, currency: string = 'USD', items: any[] = []) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items
    });
  }
  
  // Facebook Pixel tracking
  if (typeof window !== 'undefined' && window.fbq) {
    window.fbq('track', 'Purchase', {
      value: value,
      currency: currency
    });
  }
};

// Course enrollment tracking
export const trackCourseEnrollment = (courseId: string, courseName: string, price: number) => {
  trackEvent('course_enrollment', {
    course_id: courseId,
    course_name: courseName,
    value: price,
    currency: 'USD',
    event_category: 'engagement',
    event_label: courseName
  });
  
  // Track as conversion
  trackPurchase(Date.now().toString(), price, 'USD', [{
    item_id: courseId,
    item_name: courseName,
    category: 'Course',
    quantity: 1,
    price: price
  }]);
};

// Lead generation tracking
export const trackLeadGeneration = (source: string, email?: string) => {
  trackEvent('generate_lead', {
    source: source,
    method: 'email_signup',
    event_category: 'lead_generation',
    event_label: source
  });
  
  // Facebook Pixel lead tracking
  if (typeof window !== 'undefined' && window.fbq) {
    window.fbq('track', 'Lead');
  }
};

// Video engagement tracking
export const trackVideoPlay = (videoTitle: string, videoId: string, progress: number = 0) => {
  trackEvent('video_play', {
    video_title: videoTitle,
    video_id: videoId,
    progress: progress,
    event_category: 'video',
    event_label: videoTitle
  });
};

export const trackVideoProgress = (videoTitle: string, videoId: string, progress: number) => {
  // Only track at 25%, 50%, 75%, and 100%
  if ([25, 50, 75, 100].includes(progress)) {
    trackEvent('video_progress', {
      video_title: videoTitle,
      video_id: videoId,
      progress: progress,
      event_category: 'video',
      event_label: `${videoTitle} - ${progress}%`
    });
  }
};

// Search tracking
export const trackSearchQuery = (searchTerm: string, resultsCount: number) => {
  trackEvent('search', {
    search_term: searchTerm,
    results_count: resultsCount,
    event_category: 'search',
    event_label: searchTerm
  });
};

// Button/CTA click tracking
export const trackCTAClick = (ctaText: string, location: string, destination?: string) => {
  trackEvent('cta_click', {
    cta_text: ctaText,
    cta_location: location,
    destination: destination,
    event_category: 'engagement',
    event_label: `${location} - ${ctaText}`
  });
};

// Form submission tracking
export const trackFormSubmission = (formName: string, formLocation: string, success: boolean = true) => {
  trackEvent('form_submission', {
    form_name: formName,
    form_location: formLocation,
    success: success,
    event_category: 'form',
    event_label: formName
  });
};

// Newsletter signup tracking
export const trackNewsletterSignup = (source: string) => {
  trackEvent('newsletter_signup', {
    source: source,
    event_category: 'engagement',
    event_label: `Newsletter - ${source}`
  });
  
  trackLeadGeneration(`newsletter_${source}`);
};

// Page view tracking with enhanced data
export const trackPageView = (pagePath: string, pageTitle: string, additionalData: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GTM || '', {
      page_path: pagePath,
      page_title: pageTitle,
      ...additionalData
    });
  }
};

// Scroll depth tracking
export const trackScrollDepth = (depth: number, pagePath: string) => {
  trackEvent('scroll_depth', {
    scroll_depth: depth,
    page_path: pagePath,
    event_category: 'engagement',
    event_label: `${pagePath} - ${depth}%`
  });
};

// Time on page tracking
export const trackTimeOnPage = (timeInSeconds: number, pagePath: string) => {
  // Track time milestones: 30s, 1min, 2min, 5min
  const milestones = [30, 60, 120, 300];
  
  if (milestones.includes(timeInSeconds)) {
    trackEvent('time_on_page', {
      time_seconds: timeInSeconds,
      page_path: pagePath,
      event_category: 'engagement',
      event_label: `${pagePath} - ${timeInSeconds}s`
    });
  }
};

// Exit intent tracking
export const trackExitIntent = (pagePath: string, timeOnPage: number) => {
  trackEvent('exit_intent', {
    page_path: pagePath,
    time_on_page: timeOnPage,
    event_category: 'engagement',
    event_label: `Exit Intent - ${pagePath}`
  });
};

// Social share tracking
export const trackSocialShare = (platform: string, contentType: string, contentTitle: string) => {
  trackEvent('social_share', {
    platform: platform,
    content_type: contentType,
    content_title: contentTitle,
    event_category: 'social',
    event_label: `${platform} - ${contentTitle}`
  });
};

// Download tracking
export const trackDownload = (fileName: string, fileType: string, source: string) => {
  trackEvent('file_download', {
    file_name: fileName,
    file_type: fileType,
    source: source,
    event_category: 'download',
    event_label: fileName
  });
};

// Error tracking
export const trackError = (errorMessage: string, errorLocation: string, errorType: string = 'javascript') => {
  trackEvent('error', {
    error_message: errorMessage,
    error_location: errorLocation,
    error_type: errorType,
    event_category: 'error',
    event_label: `${errorType} - ${errorLocation}`
  });
};

// User engagement score calculation
export const calculateEngagementScore = (timeOnSite: number, pagesViewed: number, interactions: number): number => {
  // Simple engagement score algorithm
  const timeScore = Math.min(timeOnSite / 300, 1) * 40; // Max 40 points for 5+ minutes
  const pageScore = Math.min(pagesViewed / 5, 1) * 30; // Max 30 points for 5+ pages
  const interactionScore = Math.min(interactions / 10, 1) * 30; // Max 30 points for 10+ interactions
  
  return Math.round(timeScore + pageScore + interactionScore);
};

// Track engagement score
export const trackEngagementScore = (score: number, sessionId: string) => {
  trackEvent('engagement_score', {
    score: score,
    session_id: sessionId,
    event_category: 'engagement',
    event_label: `Score: ${score}`
  });
};

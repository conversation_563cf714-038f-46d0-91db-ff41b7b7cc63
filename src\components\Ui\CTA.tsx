'use client'
import Link from 'next/link'
import { cn } from '@/lib/cn'
import Join from 'public/home/<USER>'
import Button from '@/components/Ui/Button'
import { useState, useEffect } from 'react'
import ImageShortcut from '@/components/Ui/Image'
import numberWithCommas from '@/lib/formatNumber'

interface props {
    count?: number
    border?: boolean
}
const CTA = ({ count, border = false }: props) => {
    const [number, setNumber] = useState(0)
    useEffect(() => {
        const interval = setInterval(() => {
            setNumber(prevNumber => {
                if (prevNumber === 30000) {
                    clearInterval(interval)
                    return prevNumber
                }
                return prevNumber + 100
            })
        }, 1)

        return () => clearInterval(interval)
    }, [])
    return (
        <section
            id="cta"
            className={cn(
                'w-full overflow-hidden py-5 text-black max-sm:pb-16 max-sm:pt-0',
                border && 'border-t border-gray-700 max-md:border-none'
            )}>
            <div className="container mx-auto flex flex-wrap items-center gap-7 max-md:flex-col max-md:items-start max-md:gap-0">
                <ImageShortcut
                    src={Join}
                    width={486}
                    height={361}
                    className="w-[486px] object-contain max-md:-ml-10 max-md:h-[241px] max-md:w-[241px]"
                    alt="Join"
                />

                <div className="flex max-w-[520px] flex-col items-start gap-[36px]">
                    <h1 className="font-sans text-h3 max-md:text-b2 max-md:font-medium">
                        Join <span className="text-blue md:font-semibold">{numberWithCommas(number)}+</span> learners
                        worldwide
                    </h1>
                    <div className="w-[274px] max-md:w-[238px]">
                        <Link aria-label="Start Now" href="/register" className="rounded-full">
                            <Button rounded variant="primary">
                                Start Now &gt;
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default CTA

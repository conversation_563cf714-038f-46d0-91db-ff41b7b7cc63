import { Check, CheckMobile } from "@public/home"

interface props {
    title: string,
    description: string
}
const Card = ({ title, description }: props) => {
    return (
        <div className='flex flex-col pt-6 mt-4 max-md:py-0 max-md:mt-2 flex-1'>
            <div className="flex flex-col max-md:flex-row max-md:items-center gap-6 max-md:gap-2">
                <div className="rounded-full max-md:self-center bg-[#00BA00] pt-[12.8px] pl-[6.58px] pr-[8.08px] pb-[9.36px] max-md:pt-[4.73px] max-md:pb-[3.46px] max-md:pr-[2.62px] max-md:pl-[2.43px] self-start shadow-[2.2439px_4.4878px_28.0488px_rgba(0,186,0,0.3)]">
                    <Check />
                    <CheckMobile />
                </div>

                <h2 className='text-b1 max-md:text-sub2 font-medium max-w-[266px]'>{title}</h2>
            </div>

            <p className='text-sub2 max-md:text-cap1 font-sans text-gray-800 max-w-[340px] mt-3'>{description}</p>
        </div>
    )
}

const WhyCU = () => {
    return (
        <section id="why_crypto_university" className="border-b pt-16 max-md:border-none text-black pb-11 max-md:pb-[3.75rem] border-gray-700 w-full">
            <div className="container mx-auto">
                <div className="flex flex-col gap-9 max-md:gap-[1.875rem]">
                    <h1 className='text-h3 max-md:text-callout font-medium max-md:font-semibold'>Why Crypto University?</h1>

                    <div className='grid grid-flow-col max-md:grid-flow-row max-md:gap-6 gap-[32px]'>
                        <Card
                            description='I have learned so much and refined so much of my knowledge about Bitcoin and Altcoins…
                                        and just being a more intentional investor than others'
                            title='Beginner Friendly' />

                        <div className="max-md:hidden w-[0.5px] bg-gray-700" />

                        <Card
                            description='We have endless amount of content. You pick and choose which area to start with based on
                             your goals or prior experience. You can also refer back to our lessons whenever faced with a challenge.'
                            title='Learn at your pace' />

                        <div className="max-md:hidden w-[1px] bg-gray-700" />

                        <Card
                            description="You'll be part of our Discord server where you can ask questions to experts and other learners."
                            title='Community Support' />
                    </div>
                </div>
            </div>
        </section>
    )
}

export default WhyCU
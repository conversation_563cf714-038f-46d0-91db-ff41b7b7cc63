'use client';

import { useEffect } from 'react';
import { trackEvent } from '@/lib/analytics';

interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    // Web Vitals monitoring
    const observeWebVitals = () => {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcp = entries[0];
        if (fcp) {
          trackEvent('web_vital_fcp', {
            value: Math.round(fcp.startTime),
            metric_type: 'fcp',
            event_category: 'performance'
          });
        }
      });

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          trackEvent('web_vital_lcp', {
            value: Math.round(lastEntry.startTime),
            metric_type: 'lcp',
            event_category: 'performance'
          });
        }
      });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fid = entries[0] as any;
        if (fid && fid.processingStart) {
          trackEvent('web_vital_fid', {
            value: Math.round(fid.processingStart - fid.startTime),
            metric_type: 'fid',
            event_category: 'performance'
          });
        }
      });

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        
        trackEvent('web_vital_cls', {
          value: Math.round(clsValue * 1000) / 1000,
          metric_type: 'cls',
          event_category: 'performance'
        });
      });

      try {
        fcpObserver.observe({ entryTypes: ['paint'] });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        fidObserver.observe({ entryTypes: ['first-input'] });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }

      // Cleanup function
      return () => {
        fcpObserver.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    };

    // Navigation timing
    const trackNavigationTiming = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics = {
            dns_lookup: Math.round(navigation.domainLookupEnd - navigation.domainLookupStart),
            tcp_connection: Math.round(navigation.connectEnd - navigation.connectStart),
            tls_negotiation: navigation.secureConnectionStart > 0 
              ? Math.round(navigation.connectEnd - navigation.secureConnectionStart) 
              : 0,
            ttfb: Math.round(navigation.responseStart - navigation.requestStart),
            response_time: Math.round(navigation.responseEnd - navigation.responseStart),
            dom_processing: Math.round(navigation.domContentLoadedEventStart - navigation.responseEnd),
            dom_complete: Math.round(navigation.domComplete - navigation.domContentLoadedEventStart),
            page_load: Math.round(navigation.loadEventEnd - (navigation as any).navigationStart)
          };

          trackEvent('navigation_timing', {
            ...metrics,
            event_category: 'performance'
          });
        }
      }
    };

    // Resource timing
    const trackResourceTiming = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        
        // Group resources by type
        const resourceTypes: { [key: string]: PerformanceResourceTiming[] } = {};
        
        resources.forEach(resource => {
          const type = resource.initiatorType || 'other';
          if (!resourceTypes[type]) {
            resourceTypes[type] = [];
          }
          resourceTypes[type].push(resource);
        });

        // Track metrics for each resource type
        Object.entries(resourceTypes).forEach(([type, typeResources]) => {
          const totalSize = typeResources.reduce((sum, resource) => sum + (resource.transferSize || 0), 0);
          const avgDuration = typeResources.reduce((sum, resource) => sum + resource.duration, 0) / typeResources.length;
          
          trackEvent('resource_timing', {
            resource_type: type,
            count: typeResources.length,
            total_size: totalSize,
            avg_duration: Math.round(avgDuration),
            event_category: 'performance'
          });
        });
      }
    };

    // Memory usage (if available)
    const trackMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        
        trackEvent('memory_usage', {
          used_heap: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
          total_heap: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
          heap_limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
          event_category: 'performance'
        });
      }
    };

    // Connection information
    const trackConnectionInfo = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        
        trackEvent('connection_info', {
          effective_type: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          save_data: connection.saveData,
          event_category: 'performance'
        });
      }
    };

    // Device information
    const trackDeviceInfo = () => {
      trackEvent('device_info', {
        user_agent: navigator.userAgent,
        screen_width: screen.width,
        screen_height: screen.height,
        viewport_width: window.innerWidth,
        viewport_height: window.innerHeight,
        device_pixel_ratio: window.devicePixelRatio,
        color_depth: screen.colorDepth,
        event_category: 'device'
      });
    };

    // Initialize monitoring
    const cleanup = observeWebVitals();
    
    // Track other metrics after page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        trackNavigationTiming();
        trackResourceTiming();
        trackMemoryUsage();
        trackConnectionInfo();
        trackDeviceInfo();
      }, 1000); // Wait 1 second after load to ensure all metrics are available
    });

    // Track performance periodically
    const performanceInterval = setInterval(() => {
      trackMemoryUsage();
    }, 30000); // Every 30 seconds

    // Cleanup
    return () => {
      if (cleanup) cleanup();
      clearInterval(performanceInterval);
    };
  }, []);

  // Error boundary for performance monitoring
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      trackEvent('javascript_error', {
        message: event.message,
        filename: event.filename,
        line_number: event.lineno,
        column_number: event.colno,
        stack: event.error?.stack,
        event_category: 'error'
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackEvent('promise_rejection', {
        reason: event.reason?.toString(),
        event_category: 'error'
      });
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return null; // This component doesn't render anything
};

export default PerformanceMonitor;

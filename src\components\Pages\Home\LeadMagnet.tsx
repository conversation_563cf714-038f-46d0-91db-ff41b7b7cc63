'use client';

import React, { useState } from 'react';
import Image from 'next/image';
// import { Button } from '@/components/Ui/Button';
import Button from '@/components/Ui/Button';

const LeadMagnet: React.FC = () => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Here you would integrate with your email service (e.g., Mailchimp, ConvertKit)
      console.log('Lead captured:', { firstName, email });
      
      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <section className="py-20 bg-gradient-to-br from-secondary via-gray-800 to-secondary">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-white rounded-2xl p-8 shadow-2xl">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4">Check Your Email!</h3>
              <p className="text-gray-600 mb-6">
                We've sent your free Crypto 101 mini-course to <strong>{email}</strong>. 
                Check your inbox (and spam folder) for instant access.
              </p>
              <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
                <p className="text-sm text-gray-700">
                  <strong>What's next?</strong> While you're learning the basics, consider joining our Alpha Group 
                  for advanced strategies and daily trading signals.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-red-900 via-black to-red-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-yellow-400/20 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-green-400/20 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-400/20 rounded-full animate-pulse delay-500"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Ultra-Aggressive Content */}
            <div className="text-white">
              {/* Massive Urgency Banner */}
              <div className="bg-red-600 text-white px-8 py-4 rounded-2xl mb-8 border-4 border-yellow-400 animate-pulse">
                <div className="text-center">
                  <div className="text-2xl font-black">🚨 WARNING: CRYPTO BULL RUN STARTING!</div>
                  <div className="text-lg font-bold">Don't Miss Out on Life-Changing Profits!</div>
                </div>
              </div>

              <h2 className="text-4xl md:text-5xl font-black mb-6 leading-tight">
                🔥 Get Our <span className="text-yellow-400">$2,847 CRYPTO BLUEPRINT</span>
                <span className="text-green-400"> ABSOLUTELY FREE!</span>
              </h2>

              <div className="bg-green-600 text-white p-6 rounded-2xl mb-8 border-2 border-yellow-400">
                <p className="text-2xl font-bold leading-relaxed text-center">
                  The EXACT system that helped <span className="text-yellow-400">47,832+ students</span> make their first $10K in crypto - even complete beginners!
                </p>
              </div>
              
              {/* Explosive Benefits */}
              <div className="bg-black/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border-2 border-yellow-400">
                <h3 className="text-3xl font-black text-yellow-400 mb-6 text-center">
                  🎯 WHAT YOU GET INSIDE (Worth $2,847):
                </h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {[
                    { text: 'Secret 5-Coin Portfolio Strategy', value: '$497' },
                    { text: 'Crypto Millionaire Mindset Training', value: '$397' },
                    { text: 'Risk-Free Trading Blueprint', value: '$297' },
                    { text: 'Market Crash Protection System', value: '$397' },
                    { text: 'Daily Profit Opportunities Guide', value: '$297' },
                    { text: 'VIP Discord Community Access', value: '$497' },
                    { text: 'Personal Success Coach Call', value: '$297' },
                    { text: 'Lifetime Updates & Support', value: '$197' }
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center justify-between bg-green-600/20 p-3 rounded-lg border border-green-400">
                      <div className="flex items-center">
                        <span className="text-green-400 mr-2 text-xl">💎</span>
                        <span className="text-white font-semibold">{benefit.text}</span>
                      </div>
                      <span className="text-yellow-400 font-bold">{benefit.value}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-6 text-center">
                  <div className="bg-yellow-400 text-black px-6 py-3 rounded-2xl inline-block">
                    <div className="text-2xl font-black">TOTAL VALUE: $2,847</div>
                    <div className="text-xl font-bold">YOURS FREE TODAY!</div>
                  </div>
                </div>
              </div>
              
              {/* Massive Social Proof */}
              <div className="bg-purple-600 text-white p-6 rounded-2xl border-2 border-yellow-400">
                <div className="text-center">
                  <div className="text-2xl font-black mb-4">🏆 SUCCESS STORIES:</div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-black/30 p-4 rounded-lg">
                      <div className="text-3xl font-black text-green-400">$89K</div>
                      <div className="text-sm">Sarah's 6-month profit</div>
                    </div>
                    <div className="bg-black/30 p-4 rounded-lg">
                      <div className="text-3xl font-black text-green-400">$156K</div>
                      <div className="text-sm">Mike's first year</div>
                    </div>
                    <div className="bg-black/30 p-4 rounded-lg">
                      <div className="text-3xl font-black text-green-400">$247K</div>
                      <div className="text-sm">Lisa quit her job!</div>
                    </div>
                  </div>
                  <div className="mt-4 text-yellow-400 font-bold">
                    Join 47,832+ students making life-changing profits!
                  </div>
                </div>
              </div>
            </div>
            
            {/* Ultra-Aggressive Form */}
            <div className="bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-3xl p-8 shadow-2xl border-4 border-red-600 relative overflow-hidden">
              {/* Animated elements */}
              <div className="absolute top-4 right-4 animate-bounce">
                <div className="text-4xl">🚀</div>
              </div>
              <div className="absolute bottom-4 left-4 animate-pulse">
                <div className="text-4xl">💰</div>
              </div>

              <div className="relative z-10">
                <div className="mb-8">
                  <div className="bg-red-600 text-white p-4 rounded-2xl mb-6 text-center animate-pulse">
                    <div className="text-2xl font-black">⚡ INSTANT ACCESS ⚡</div>
                    <div className="text-lg font-bold">Download Starts Immediately!</div>
                  </div>

                  <div className="relative w-full h-48 mb-6">
                    <Image
                      src="/images/free-course-preview.jpg"
                      alt="$2,847 Crypto Blueprint Preview"
                      fill
                      className="rounded-2xl object-cover border-4 border-black"
                    />
                    <div className="absolute inset-0 bg-black/50 rounded-2xl flex items-center justify-center">
                      <div className="bg-red-600 text-white rounded-full p-6 animate-pulse">
                        <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>

                    {/* Value overlay */}
                    <div className="absolute top-4 left-4 bg-green-600 text-white px-4 py-2 rounded-full font-bold">
                      Worth $2,847!
                    </div>
                  </div>

                  <h3 className="text-3xl font-black text-black mb-4 text-center">
                    🔥 CLAIM YOUR FREE $2,847 BLUEPRINT NOW!
                  </h3>
                  <div className="bg-black text-yellow-400 p-4 rounded-2xl text-center">
                    <p className="text-xl font-bold">
                      Join 47,832+ students making $10K+/month with crypto!
                    </p>
                  </div>
                </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="bg-red-600 text-white p-6 rounded-2xl">
                  <div className="text-center mb-4">
                    <div className="text-2xl font-black">⏰ HURRY! LIMITED TIME OFFER!</div>
                    <div className="text-lg font-bold">Only 47 copies left at this price!</div>
                  </div>

                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Enter Your First Name"
                    className="w-full px-6 py-4 text-black text-lg font-semibold rounded-xl border-4 border-yellow-400 focus:ring-4 focus:ring-yellow-300 mb-4"
                    required
                  />
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter Your Best Email Address"
                    className="w-full px-6 py-4 text-black text-lg font-semibold rounded-xl border-4 border-yellow-400 focus:ring-4 focus:ring-yellow-300 mb-6"
                    required
                  />

                  <Button
                    type="submit"
                    variant="primary"
                    size="xl"
                    loading={isSubmitting}
                    className="w-full text-2xl font-black py-6 bg-green-600 hover:bg-green-700 hover:scale-105 transform transition-all shadow-2xl"
                  >
                    {isSubmitting ? '🔄 SENDING YOUR BLUEPRINT...' : '🚀 YES! SEND ME THE $2,847 BLUEPRINT FREE!'}
                  </Button>
                </div>
              </form>

              {/* Final Trust Elements */}
              <div className="mt-6 text-center">
                <div className="bg-black text-yellow-400 p-4 rounded-2xl mb-4">
                  <div className="text-lg font-bold">🔒 100% FREE - NO CREDIT CARD REQUIRED</div>
                  <div className="text-sm">Instant download. No spam. Unsubscribe anytime.</div>
                </div>

                <div className="flex items-center justify-center gap-6 text-sm text-black font-semibold">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    <span>SSL Secured</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Instant Access</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <span>47,832+ Members</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LeadMagnet;

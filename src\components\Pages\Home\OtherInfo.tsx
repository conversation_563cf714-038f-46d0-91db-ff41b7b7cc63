import Link from 'next/link'
import <PERSON><PERSON> from '@/components/Ui/Button'

const OtherInfo = () => {
    const data = [
        {
            title: 'New Course',
            desc: 'Looking for your next course? Find out content here',
            button: 'Browse Course',
        },
        {
            title: '1 ON 1 Coaching',
            desc: 'Get exclusive 1 on 1 Mentorship from our Crypto experts',
            button: 'Book Now',
        },
    ]

    return (
        <section id="other" className="w-full text-black max-md:pb-[7.3125rem]">
            <div className="container mx-auto flex justify-between max-md:flex-col">
                {/* Col 1 */}
                <div className="flex flex-grow flex-col justify-between gap-[24px] border-r border-gray-700 pb-[4.25rem] pr-16 pt-[5.25rem] max-md:gap-[1.125rem] max-md:border-none max-md:pb-[4.25rem] max-md:pr-0 max-md:pt-0">
                    <div className="flex flex-col gap-[12px]">
                        <p className="font-sans text-sub3">{data[0].title}</p>
                        <h1 className="max-w-[540px] text-b1 font-medium max-md:text-b2">{data[0].desc}</h1>
                    </div>
                    <div className="flex items-center justify-between">
                        <Link
                            aria-label="Browse Course"
                            href={'/products/#courses'}
                            className="w-[272px] rounded-full max-md:w-[222px]">
                            <Button rounded>{data[0].button} &gt;</Button>
                        </Link>
                    </div>
                </div>

                {/* Col 2 */}
                <div className="ml-16 flex flex-grow flex-col gap-[24px] pb-[4.25rem] pt-[5.25rem] max-md:ml-0 max-md:gap-[1.125rem] max-md:pb-0 max-md:pt-0">
                    <div className="flex flex-col gap-[12px]">
                        <p className="font-sans text-sub3">{data[1].title}</p>
                        <h1 className="max-w-[540px] text-b1 font-medium max-md:text-b2">{data[1].desc}</h1>
                    </div>
                    <div className="flex items-center justify-between ">
                        <Link
                            aria-label="Book Now"
                            href={'/consultation'}
                            className="w-[272px] rounded-full max-md:w-[222px]">
                            <Button rounded>{data[1].button} &gt;</Button>
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default OtherInfo

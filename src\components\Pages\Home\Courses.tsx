import Link from 'next/link'
import { CourseModel } from '@/types/CourseModel'
import CourseCard from './CourseCard'

interface CoursesProps {
    courses: CourseModel[]
}

const Courses = ({ courses }: CoursesProps) => {
    return (
        <section
            id="courses"
            className="w-full py-20 bg-gray-50">
            <div className="container mx-auto px-4">
                <div className="flex flex-col gap-12">
                    {/* Section Header */}
                    <div className="text-center max-w-4xl mx-auto">
                        <h2 className="text-3xl md:text-4xl font-bold mb-4">
                            Cryptocurrency Education That Delivers <span className="text-primary">Real Results</span>
                        </h2>
                        <p className="text-xl text-gray-600 leading-relaxed">
                            From beginner fundamentals to advanced trading strategies — learn at your own pace with
                            <span className="font-semibold text-primary"> 10,000+ hours</span> of expert-led content
                        </p>

                        {/* Trust Indicators */}
                        <div className="flex items-center justify-center gap-8 mt-8 text-sm text-gray-500">
                            <div className="flex items-center gap-2">
                                <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>30,000+ Students</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Expert-Led Content</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Lifetime Access</span>
                            </div>
                        </div>
                    </div>

                    {/* Courses Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {courses.map((item: CourseModel, index: number) => (
                            <CourseCard key={index} course={item} />
                        ))}
                    </div>

                    {/* View All Courses CTA */}
                    <div className="text-center">
                        <Link
                            href="/courses"
                            className="inline-flex items-center text-primary hover:text-primary-dark font-medium text-lg group"
                        >
                            View All Courses
                            <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Courses

"use client";
import Image from 'next/image'

export default function EnhancedNewsletter() {
  return (
    <section className="bg-black py-12">
      <div className="container mx-auto flex flex-col md:flex-row items-center gap-8">
        <div className="flex-1">
          <h3 className="text-h2 font-bold text-yellow mb-2">Get a Free Crypto 101 Mini-Course</h3>
          <p className="text-b3 text-white/80 mb-4">
            Sign up and receive exclusive lessons, tips, and community access. No spam, ever.
          </p>
          <form className="flex gap-2" onSubmit={e => e.preventDefault()}>
            <input
              type="email"
              placeholder="Your email"
              className="rounded-l-full px-6 py-3 text-black bg-white focus:outline-yellow"
              required
            />
            <button
              type="submit"
              className="bg-yellow text-black rounded-r-full px-8 py-3 font-bold hover:bg-yellow-400 transition"
            >
              Get Access
            </button>
          </form>
        </div>
        <div className="flex-1 flex justify-center">
          <Image
            src="/newsletter-illustration.svg"
            alt="Newsletter"
            width={256}
            height={256}
            className="w-64"
          />
        </div>
      </div>
    </section>
  )
}
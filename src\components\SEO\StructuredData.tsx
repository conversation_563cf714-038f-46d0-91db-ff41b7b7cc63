import React from 'react';
import Head from 'next/head';

interface CourseStructuredDataProps {
  course: {
    name: string;
    description: string;
    provider: string;
    price: number;
    currency: string;
    image: string;
    url: string;
    instructor?: string;
    duration?: string;
    level?: string;
    rating?: number;
    reviewCount?: number;
  };
}

interface OrganizationStructuredDataProps {
  organization: {
    name: string;
    url: string;
    logo: string;
    description: string;
    foundingDate?: string;
    founder?: string;
    sameAs?: string[];
    contactPoint?: {
      telephone: string;
      contactType: string;
      email: string;
    };
  };
}

interface FAQStructuredDataProps {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

export const CourseStructuredData: React.FC<CourseStructuredDataProps> = ({ course }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: course.name,
    description: course.description,
    provider: {
      '@type': 'Organization',
      name: course.provider,
      url: 'https://cryptouniversity.network'
    },
    offers: {
      '@type': 'Offer',
      price: course.price,
      priceCurrency: course.currency,
      availability: 'https://schema.org/InStock'
    },
    image: course.image,
    url: course.url,
    ...(course.instructor && {
      instructor: {
        '@type': 'Person',
        name: course.instructor
      }
    }),
    ...(course.duration && { timeRequired: course.duration }),
    ...(course.level && { educationalLevel: course.level }),
    ...(course.rating && course.reviewCount && {
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: course.rating,
        reviewCount: course.reviewCount,
        bestRating: 5,
        worstRating: 1
      }
    })
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

export const OrganizationStructuredData: React.FC<OrganizationStructuredDataProps> = ({ organization }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: organization.name,
    url: organization.url,
    logo: organization.logo,
    description: organization.description,
    ...(organization.foundingDate && { foundingDate: organization.foundingDate }),
    ...(organization.founder && {
      founder: {
        '@type': 'Person',
        name: organization.founder
      }
    }),
    ...(organization.sameAs && { sameAs: organization.sameAs }),
    ...(organization.contactPoint && {
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: organization.contactPoint.telephone,
        contactType: organization.contactPoint.contactType,
        email: organization.contactPoint.email
      }
    })
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

export const FAQStructuredData: React.FC<FAQStructuredDataProps> = ({ faqs }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

export const WebsiteStructuredData: React.FC = () => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Crypto University',
    url: 'https://cryptouniversity.network',
    description: 'The world\'s #1 Cryptocurrency education platform. Get access to our customized courses to help you succeed in crypto',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://cryptouniversity.network/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Crypto University',
      logo: {
        '@type': 'ImageObject',
        url: 'https://cryptouniversity.network/cu-black.png'
      }
    }
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

export const BreadcrumbStructuredData: React.FC<{ items: Array<{ name: string; url: string }> }> = ({ items }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  );
};

import { cn } from '@/lib/cn'
import { FC, ReactNode } from 'react'

interface Props {
    variant?: 'default' | 'primary' | 'secondary' | 'dashed' | 'accent' | 'outline' | 'ghost'
    size?: 'sm' | 'md' | 'lg' | 'xl'
    rounded?: boolean
    nav?: boolean
    children: ReactNode
    onClick?: ((e: any) => Promise<void>) | (() => void) | ((e:any) => void)
    type?: 'button' | 'submit' | 'reset'
    className?: string
    disabled?: boolean
    gradient?: boolean
    loading?: boolean
    icon?: ReactNode
}

const Button: FC<Props> = ({
    variant = 'default',
    size = 'md',
    rounded = false,
    nav = false,
    children,
    onClick,
    className,
    type = 'button',
    disabled = false,
    gradient = false,
    loading = false,
    icon,
}) => {
    const className1 = cn(
        'flex items-center gap-2 justify-center font-sans select-none w-full font-semibold min-w-fit transition-all duration-200 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50',
        className,
        {
            // Size variants
            'px-3 py-2 text-sm': size === 'sm' && !nav,
            'px-4 py-3 text-base': size === 'md' && !nav,
            'px-6 py-4 text-lg': size === 'lg' && !nav,
            'px-8 py-5 text-xl': size === 'xl' && !nav,

            // Legacy nav sizing
            'py-[13.5px] px-6 text-sub2 max-md:py-4 max-md:text-sub3': nav,
            'py-[17.5px] px-6 text-sub2 max-md:py-4 max-md:text-sub3': !nav && !size,

            // Border radius
            'rounded-full': rounded,
            'rounded-lg': !rounded,

            // Gradient effect
            'gradient': gradient,

            // Enhanced variant styles with new brand colors
            'bg-primary hover:bg-primary-dark text-secondary focus:ring-primary shadow-md hover:shadow-lg': variant === 'primary',
            'bg-secondary hover:bg-gray-800 text-white focus:ring-gray-500 shadow-md hover:shadow-lg': variant === 'secondary',
            'border-2 border-primary text-primary hover:bg-primary hover:text-secondary focus:ring-primary': variant === 'outline',
            'text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-300': variant === 'ghost',
            'bg-accent hover:bg-blue-600 text-white focus:ring-accent shadow-md hover:shadow-lg': variant === 'accent',
            'border-[1px] border-dashed border-black hover:bg-blue-100 text-black active:opacity-60 disabled:opacity-40': variant === 'dashed',
            'bg-white border border-gray-600 text-black hover:bg-gray-500 hover:border-gray-500 active:border-gray-600 active:bg-gray-600 disabled:opacity-30': variant === 'default',
        }
    )

    return (
        <button
            type={type}
            className={className1}
            onClick={onClick}
            disabled={disabled || loading}
        >
            {loading && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            )}
            {icon && !loading && <span className="mr-2">{icon}</span>}
            {children}
        </button>
    )
}

export default Button
import Link from 'next/link'
import { cn } from '@/lib/cn'
import But<PERSON> from '@/components/Ui/Button'
import HeroImage from 'public/home/<USER>'
import numberWithCommas from '@/lib/formatNumber'
import ImageShortcut from '@/components/Ui/Image'
import { Counters } from '@/components/Ui/Counters'

interface props {
    number: number
    unit: string
    icon: JSX.Element
    className?: string
}

const Counter = ({ number, unit, icon, className }: props) => {
    return (
        <div className={cn('flex items-center gap-4 border-r border-gray-200 max-md:gap-2', className)}>
            {icon}
            <div className="flex flex-col pr-9 max-md:pr-0">
                <div className="text-b1 font-medium max-md:text-sub1">{numberWithCommas(number)}+</div>
                <p className="w-full text-sub2 max-md:text-cap2">{unit}</p>
            </div>
        </div>
    )
}

const Hero = () => {
    return (
        <section
            id="hero"
            className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-red-900 via-black to-red-900">

            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-yellow-400/20 rounded-full blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse delay-500"></div>
            </div>

            {/* Floating Money Symbols */}
            <div className="absolute inset-0 pointer-events-none">
                <div className="absolute top-20 left-20 text-6xl animate-bounce delay-300">💰</div>
                <div className="absolute top-40 right-32 text-4xl animate-bounce delay-700">🚀</div>
                <div className="absolute bottom-32 left-40 text-5xl animate-bounce delay-1000">💎</div>
                <div className="absolute bottom-20 right-20 text-4xl animate-bounce delay-500">📈</div>
            </div>

            <div className="container relative mx-auto px-4 py-20 z-10">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Content - Ultra Aggressive */}
                    <div className="flex flex-col gap-8 animate-fade-in text-white">
                        <div className="flex flex-col gap-6">
                            {/* Urgency Banner */}
                            <div className="bg-red-600 text-white px-6 py-3 rounded-full w-fit animate-pulse border-2 border-yellow-400">
                                <div className="flex items-center gap-2 font-bold text-lg">
                                    <span className="animate-spin">⚡</span>
                                    <span>LIMITED TIME: 67% OFF ENDS SOON!</span>
                                    <span className="animate-spin">⚡</span>
                                </div>
                            </div>

                            {/* Massive Social Proof */}
                            <div className="bg-green-600 text-white px-6 py-4 rounded-2xl border-2 border-yellow-400">
                                <div className="flex items-center justify-center gap-4 text-center">
                                    <div>
                                        <div className="text-3xl font-black">47,832</div>
                                        <div className="text-sm">Students Made Profits</div>
                                    </div>
                                    <div className="w-px h-12 bg-white/30"></div>
                                    <div>
                                        <div className="text-3xl font-black">$2.4M+</div>
                                        <div className="text-sm">Total Profits Generated</div>
                                    </div>
                                    <div className="w-px h-12 bg-white/30"></div>
                                    <div>
                                        <div className="text-3xl font-black">4.9★</div>
                                        <div className="text-sm">Average Rating</div>
                                    </div>
                                </div>
                            </div>

                            <h1 className="text-5xl md:text-6xl lg:text-7xl font-black leading-tight text-center lg:text-left">
                                <span className="text-red-400">STOP</span> Being <span className="text-yellow-400">BROKE!</span><br/>
                                <span className="text-green-400">MASTER</span> Crypto & Make <span className="text-primary animate-pulse">$10K+/Month</span>
                            </h1>

                            <div className="bg-black/50 backdrop-blur-sm rounded-2xl p-6 border-2 border-yellow-400">
                                <p className="text-2xl text-yellow-100 leading-relaxed font-bold text-center">
                                    🔥 Join <span className="text-yellow-400 font-black">47,832+ students</span> who&apos;ve quit their jobs and achieved financial freedom through our <span className="text-green-400">PROVEN</span> crypto system!
                                </p>

                                {/* Testimonial Ticker */}
                                <div className="mt-4 bg-green-600 rounded-lg p-4">
                                    <div className="text-center">
                                        <div className="text-xl font-bold">&quot;I made $89,432 in my first 6 months!&quot;</div>
                                        <div className="text-green-200">- Sarah M., Alpha Group Member</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Ultra-Aggressive CTA Buttons */}
                        <div className="flex flex-col gap-6">
                            {/* Primary CTA - Massive and Attention-Grabbing */}
                            <Link href="/register" className="w-full">
                                <div className="bg-gradient-to-r from-green-500 via-green-600 to-green-700 hover:from-green-600 hover:via-green-700 hover:to-green-800 text-white p-8 rounded-3xl border-4 border-yellow-400 shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 cursor-pointer">
                                    <div className="text-center">
                                        <div className="text-4xl md:text-5xl font-black mb-2">
                                            🚀 START MAKING MONEY TODAY!
                                        </div>
                                        <div className="text-2xl font-bold mb-4">
                                            Get Instant Access to Our $3,276 Course Bundle for Just $97
                                        </div>
                                        <div className="bg-red-600 text-white px-6 py-3 rounded-full inline-block animate-pulse">
                                            <span className="text-xl font-bold">⏰ LIMITED TIME: 67% OFF!</span>
                                        </div>
                                    </div>
                                </div>
                            </Link>

                            {/* Secondary CTA */}
                            <div className="grid md:grid-cols-2 gap-4">
                                <Link href="/courses" className="w-full">
                                    <div className="bg-yellow-400 hover:bg-yellow-500 text-black p-6 rounded-2xl border-2 border-black shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer">
                                        <div className="text-center">
                                            <div className="text-2xl font-black mb-2">📚 FREE CRYPTO COURSE</div>
                                            <div className="text-lg font-bold">Learn the Basics (Worth $297)</div>
                                        </div>
                                    </div>
                                </Link>

                                <Link href="/memberships" className="w-full">
                                    <div className="bg-purple-600 hover:bg-purple-700 text-white p-6 rounded-2xl border-2 border-yellow-400 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer">
                                        <div className="text-center">
                                            <div className="text-2xl font-black mb-2">👑 VIP ALPHA GROUP</div>
                                            <div className="text-lg font-bold">Daily Signals & Mentorship</div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        </div>

                        {/* Scarcity and Social Proof */}
                        <div className="bg-red-600 text-white p-6 rounded-2xl border-2 border-yellow-400">
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div>
                                    <div className="text-3xl font-black text-yellow-400">47,832</div>
                                    <div className="text-sm">Success Stories</div>
                                </div>
                                <div>
                                    <div className="text-3xl font-black text-yellow-400">$2.4M+</div>
                                    <div className="text-sm">Profits Made</div>
                                </div>
                                <div>
                                    <div className="text-3xl font-black text-yellow-400">97%</div>
                                    <div className="text-sm">Success Rate</div>
                                </div>
                                <div>
                                    <div className="text-3xl font-black text-yellow-400 animate-pulse">23</div>
                                    <div className="text-sm">Joined Today</div>
                                </div>
                            </div>

                            <div className="mt-4 text-center">
                                <div className="bg-yellow-400 text-black px-4 py-2 rounded-full inline-block font-bold animate-pulse">
                                    ⚠️ ONLY 47 SPOTS LEFT AT THIS PRICE!
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Ultra-Aggressive Hero Image Section */}
                    <div className="relative lg:block hidden">
                        <div className="relative">
                            <ImageShortcut
                                src={HeroImage}
                                alt="Crypto University Success Dashboard"
                                width={704}
                                height={704}
                                priority
                                className="w-full h-auto rounded-3xl shadow-2xl animate-slide-up border-4 border-yellow-400"
                            />

                            {/* Explosive Floating Elements */}
                            <div className="absolute -top-8 -left-8 bg-green-600 text-white rounded-2xl shadow-2xl p-6 animate-bounce border-4 border-yellow-400">
                                <div className="text-center">
                                    <div className="text-3xl font-black">+$47K</div>
                                    <div className="text-sm font-bold">This Month!</div>
                                </div>
                            </div>

                            <div className="absolute -top-4 -right-8 bg-red-600 text-white rounded-2xl shadow-2xl p-4 animate-pulse border-4 border-yellow-400">
                                <div className="text-center">
                                    <div className="text-2xl font-black">🔥 LIVE</div>
                                    <div className="text-xs font-bold">Trading Now</div>
                                </div>
                            </div>

                            <div className="absolute -bottom-8 -right-8 bg-yellow-400 text-black rounded-2xl shadow-2xl p-6 animate-bounce delay-500 border-4 border-green-600">
                                <div className="text-center">
                                    <div className="text-3xl font-black">2,847%</div>
                                    <div className="text-sm font-bold">ROI Achieved</div>
                                </div>
                            </div>

                            <div className="absolute -bottom-4 -left-8 bg-purple-600 text-white rounded-2xl shadow-2xl p-4 animate-pulse delay-1000 border-4 border-yellow-400">
                                <div className="text-center">
                                    <div className="text-xl font-black">👑 VIP</div>
                                    <div className="text-xs font-bold">Member</div>
                                </div>
                            </div>

                            {/* Success Ticker */}
                            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/80 backdrop-blur-sm text-white rounded-2xl p-6 border-4 border-green-400">
                                <div className="text-center">
                                    <div className="text-2xl font-black text-green-400 mb-2">💰 PROFIT ALERT!</div>
                                    <div className="text-lg font-bold">Sarah just made $12,847</div>
                                    <div className="text-sm text-green-400">2 minutes ago</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Hero

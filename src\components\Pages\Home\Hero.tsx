import Link from 'next/link'
import { cn } from '@/lib/cn'
import But<PERSON> from '@/components/Ui/Button'
import HeroImage from 'public/home/<USER>'
import numberWithCommas from '@/lib/formatNumber'
import ImageShortcut from '@/components/Ui/Image'
import { Counters } from '@/components/Ui/Counters'

interface props {
    number: number
    unit: string
    icon: JSX.Element
    className?: string
}

const Counter = ({ number, unit, icon, className }: props) => {
    return (
        <div className={cn('flex items-center gap-4 border-r border-gray-200 max-md:gap-2', className)}>
            {icon}
            <div className="flex flex-col pr-9 max-md:pr-0">
                <div className="text-b1 font-medium max-md:text-sub1">{numberWithCommas(number)}+</div>
                <p className="w-full text-sub2 max-md:text-cap2">{unit}</p>
            </div>
        </div>
    )
}

const Hero = () => {
    return (
        <section
            id="hero"
            className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-gray-50 to-white">

            {/* Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent/10 rounded-full blur-3xl"></div>
            </div>

            <div className="container relative mx-auto px-4 py-20">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Content */}
                    <div className="flex flex-col gap-8 animate-fade-in">
                        <div className="flex flex-col gap-6">
                            {/* Trust Badge */}
                            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full w-fit">
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                <span className="font-medium text-sm">4.5★ Trustpilot Rating</span>
                            </div>

                            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                                Master <span className="text-primary">Cryptocurrency</span> Trading & Investing
                            </h1>

                            <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                                Join <span className="font-semibold text-primary">30,000+ students worldwide</span> who've transformed their financial future through our proven cryptocurrency courses and mentorship programs
                            </p>
                        </div>

                        {/* CTA Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4">
                            <Link href="/courses" className="flex-1 sm:flex-none">
                                <Button variant="primary" size="lg" className="w-full sm:w-auto">
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    Explore Courses
                                </Button>
                            </Link>
                            <Link href="/memberships" className="flex-1 sm:flex-none">
                                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Join Alpha Group
                                </Button>
                            </Link>
                        </div>

                        {/* Trust Indicators */}
                        <div className="flex items-center gap-8 pt-8 border-t border-gray-200">
                            <div className="flex flex-col">
                                <span className="text-3xl font-bold text-primary">30K+</span>
                                <span className="text-gray-500 text-sm">Students</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="text-3xl font-bold text-primary">10K+</span>
                                <span className="text-gray-500 text-sm">Hours of Content</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="text-3xl font-bold text-primary">4.5★</span>
                                <span className="text-gray-500 text-sm">Trustpilot</span>
                            </div>
                            <div className="flex flex-col">
                                <span className="text-3xl font-bold text-primary">85%</span>
                                <span className="text-gray-500 text-sm">5-Star Reviews</span>
                            </div>
                        </div>
                    </div>

                    {/* Hero Image */}
                    <div className="relative lg:block hidden">
                        <div className="relative">
                            <ImageShortcut
                                src={HeroImage}
                                alt="Crypto University Dashboard Preview"
                                width={704}
                                height={704}
                                priority
                                className="w-full h-auto rounded-2xl shadow-2xl animate-slide-up"
                            />

                            {/* Floating Elements */}
                            <div className="absolute -top-6 -left-6 bg-white rounded-xl shadow-lg p-4 animate-pulse-slow">
                                <div className="flex items-center gap-3">
                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span className="text-sm font-medium">Live Trading Signals</span>
                                </div>
                            </div>

                            <div className="absolute -bottom-6 -right-6 bg-primary text-secondary rounded-xl shadow-lg p-4 animate-pulse-slow">
                                <div className="text-center">
                                    <div className="text-2xl font-bold">400%+</div>
                                    <div className="text-sm">Avg. Returns</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Hero

'use client';

import React from 'react';
import Image from 'next/image';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  avatar: string;
  quote: string;
  rating: number;
  result?: string;
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Software Engineer',
    avatar: '/testimonials/sarah.jpg',
    quote: 'The Alpha Group signals helped me generate 400% returns in just 6 months. The community support is incredible and Grey&apos;s analysis is spot on.',
    rating: 5,
    result: 'Paid off $50K debt in 8 months'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Marketing Manager',
    avatar: '/testimonials/michael.jpg',
    quote: 'I went from complete beginner to confidently trading crypto. The step-by-step courses and mentorship program changed my financial future.',
    rating: 5,
    result: 'Generated $100K+ in profits'
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Freelance Designer',
    avatar: '/testimonials/emily.jpg',
    quote: 'The trading indicators and signals are incredibly accurate. I\'ve been consistently profitable since joining the community 2 years ago.',
    rating: 5,
    result: 'Achieved financial freedom'
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'Business Owner',
    avatar: '/testimonials/david.jpg',
    quote: 'Best investment I\'ve ever made. The education quality is top-notch and the Discord community is like having 24/7 trading mentors.',
    rating: 5,
    result: 'Built 7-figure portfolio'
  },
  {
    id: '5',
    name: 'Lisa Park',
    role: 'Teacher',
    avatar: '/testimonials/lisa.jpg',
    quote: 'Started with just $1000 and now I\'m making more from crypto than my day job. The courses are easy to follow and incredibly detailed.',
    rating: 5,
    result: 'Replaced full-time income'
  },
  {
    id: '6',
    name: 'James Wilson',
    role: 'IT Consultant',
    avatar: '/testimonials/james.jpg',
    quote: 'The mentorship program is worth every penny. Having direct access to successful traders accelerated my learning curve dramatically.',
    rating: 5,
    result: 'Retired early at 35'
  }
];

const EnhancedTestimonials: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our Students Are Saying
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Join thousands of successful traders who&apos;ve transformed their crypto journey with our education
          </p>
          
          {/* Trustpilot Rating */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="flex items-center gap-2">
              <Image 
                src="/images/trustpilot-logo.svg" 
                alt="Trustpilot" 
                width={120}
                height={30}
                className="h-8 w-auto"
              />
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg 
                    key={star} 
                    className={`w-5 h-5 ${star <= 4.5 ? 'text-green-500' : 'text-gray-300'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="font-medium text-gray-700">4.5/5 (350+ reviews)</span>
            </div>
            
            <div className="text-primary font-semibold">
              85% 5-star reviews
            </div>
          </div>
        </div>
        
        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {testimonials.map((testimonial) => (
            <div 
              key={testimonial.id} 
              className="bg-gray-50 p-6 rounded-xl hover:shadow-lg transition-shadow duration-300"
            >
              {/* Rating Stars */}
              <div className="flex items-center mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg 
                    key={star} 
                    className={`w-5 h-5 ${star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              
              {/* Quote */}
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                &quot;{testimonial.quote}&quot;
              </blockquote>
              
              {/* Result Badge */}
              {testimonial.result && (
                <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium mb-4 inline-block">
                  ✨ {testimonial.result}
                </div>
              )}
              
              {/* Author */}
              <div className="flex items-center">
                <div className="relative w-12 h-12 mr-4">
                  <Image 
                    src={testimonial.avatar} 
                    alt={testimonial.name} 
                    fill
                    className="rounded-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-medium text-gray-900">{testimonial.name}</div>
                  <div className="text-gray-500 text-sm">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* CTA */}
        <div className="text-center">
          <a 
            href="/testimonials" 
            className="inline-flex items-center text-primary hover:text-primary-dark font-medium text-lg group"
          >
            Read more success stories
            <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default EnhancedTestimonials;

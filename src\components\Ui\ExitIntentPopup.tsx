'use client';

import React, { useState, useEffect } from 'react';
import { Button } from './Button';

interface ExitIntentPopupProps {
  title?: string;
  subtitle?: string;
  discount?: string;
  ctaText?: string;
  onClose?: () => void;
  onSubmit?: (email: string) => void;
}

const ExitIntentPopup: React.FC<ExitIntentPopupProps> = ({
  title = "Wait! Don't Miss Out",
  subtitle = "Get 20% off your first course with our exclusive welcome discount.",
  discount = "20%",
  ctaText = "Get My 20% Discount",
  onClose,
  onSubmit
}) => {
  const [showPopup, setShowPopup] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    // Check if popup has already been shown in this session
    const popupShown = sessionStorage.getItem('exitIntentShown');
    if (popupShown) {
      setHasShown(true);
      return;
    }

    let timeoutId: NodeJS.Timeout;
    
    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from the top of the page
      if (e.clientY <= 0 && !hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    const handleScroll = () => {
      // Show popup after user scrolls 70% of the page
      const scrollPercent = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      if (scrollPercent > 70 && !hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    // Show popup after 30 seconds as fallback
    timeoutId = setTimeout(() => {
      if (!hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    }, 30000);

    document.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('scroll', handleScroll);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, [hasShown]);

  const handleClose = () => {
    setShowPopup(false);
    if (onClose) onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);
    
    try {
      if (onSubmit) {
        await onSubmit(email);
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close popup after successful submission
      handleClose();
      
      // Show success message (you could replace this with a toast notification)
      alert('Success! Check your email for the discount code.');
      
    } catch (error) {
      console.error('Error submitting email:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!showPopup) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 animate-fade-in">
      <div className="bg-white rounded-2xl max-w-md w-full relative animate-slide-up shadow-2xl">
        {/* Close button */}
        <button 
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors z-10"
          aria-label="Close popup"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {/* Header with gradient */}
        <div className="bg-gradient-primary rounded-t-2xl p-6 text-center">
          <div className="text-6xl mb-2">🎉</div>
          <h3 className="text-2xl font-bold text-secondary mb-2">{title}</h3>
          <div className="bg-secondary/20 text-secondary px-4 py-2 rounded-full inline-block">
            <span className="font-bold text-lg">{discount} OFF</span>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 mb-6 text-center leading-relaxed">
            {subtitle}
          </p>
          
          {/* Benefits */}
          <ul className="space-y-2 mb-6">
            {[
              'Instant access to premium courses',
              'Lifetime updates and support',
              'Join 30,000+ successful students',
              'Money-back guarantee'
            ].map((benefit, index) => (
              <li key={index} className="flex items-center text-sm text-gray-700">
                <svg className="w-4 h-4 text-primary mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                {benefit}
              </li>
            ))}
          </ul>
          
          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input 
                type="email" 
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              />
            </div>
            
            <Button 
              type="submit"
              variant="primary"
              size="lg"
              loading={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? 'Processing...' : ctaText}
            </Button>
          </form>
          
          {/* Footer */}
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Limited time offer. No spam, unsubscribe anytime.
            </p>
            
            {/* Countdown timer (optional) */}
            <div className="mt-3 flex items-center justify-center gap-2 text-sm text-gray-600">
              <svg className="w-4 h-4 text-warning" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span>Offer expires in 10 minutes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExitIntentPopup;

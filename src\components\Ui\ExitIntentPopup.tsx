'use client';

import React, { useState, useEffect } from 'react';
// import { Button } from './Button';
import Button from './Button';

interface ExitIntentPopupProps {
  title?: string;
  subtitle?: string;
  discount?: string;
  ctaText?: string;
  onClose?: () => void;
  onSubmit?: (email: string) => void;
}

const ExitIntentPopup: React.FC<ExitIntentPopupProps> = ({
  title = "🚨 WAIT! You're About to Miss Out on $2,847 Worth of Crypto Education",
  subtitle = "Join 30,000+ students who've made life-changing profits. Get 67% OFF + exclusive bonuses worth $1,200 - but only for the next 10 minutes!",
  discount = "67%",
  ctaText = "🔥 YES! Give Me 67% OFF + Bonuses ($1,200 Value)",
  onClose,
  onSubmit
}) => {
  const [showPopup, setShowPopup] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasShown, setHasShown] = useState(false);
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes countdown
  const [urgencyLevel, setUrgencyLevel] = useState(0);

  useEffect(() => {
    // Check if popup has already been shown in this session
    const popupShown = sessionStorage.getItem('exitIntentShown');
    if (popupShown) {
      setHasShown(true);
      return;
    }

    let timeoutId: NodeJS.Timeout;

    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from the top of the page
      if (e.clientY <= 0 && !hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    const handleScroll = () => {
      // Show popup after user scrolls 50% of the page (more aggressive)
      const scrollPercent = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      if (scrollPercent > 50 && !hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    // Show popup after 20 seconds as fallback (more aggressive)
    timeoutId = setTimeout(() => {
      if (!hasShown) {
        setShowPopup(true);
        setHasShown(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    }, 20000);

    document.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('scroll', handleScroll);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, [hasShown]);

  // Countdown timer effect
  useEffect(() => {
    if (!showPopup) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setUrgencyLevel(3); // Maximum urgency
          return 0;
        }

        // Increase urgency as time runs out
        if (prev <= 120) setUrgencyLevel(2); // Last 2 minutes
        else if (prev <= 300) setUrgencyLevel(1); // Last 5 minutes

        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [showPopup]);

  const handleClose = () => {
    setShowPopup(false);
    if (onClose) onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);
    
    try {
      if (onSubmit) {
        await onSubmit(email);
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close popup after successful submission
      handleClose();
      
      // Show success message (you could replace this with a toast notification)
      alert('Success! Check your email for the discount code.');
      
    } catch (error) {
      console.error('Error submitting email:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!showPopup) return null;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getUrgencyColor = () => {
    if (urgencyLevel >= 2) return 'text-red-600 animate-pulse';
    if (urgencyLevel >= 1) return 'text-orange-600';
    return 'text-primary';
  };

  const getUrgencyBg = () => {
    if (urgencyLevel >= 2) return 'bg-red-600';
    if (urgencyLevel >= 1) return 'bg-orange-600';
    return 'bg-primary';
  };

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 animate-fade-in backdrop-blur-sm">
      <div className="bg-white rounded-3xl max-w-2xl w-full relative animate-slide-up shadow-2xl border-4 border-red-500">
        {/* Urgency Banner */}
        <div className={`${getUrgencyBg()} text-white text-center py-2 rounded-t-3xl`}>
          <div className="flex items-center justify-center gap-2 font-bold">
            <span className="animate-pulse">⚡</span>
            <span>LIMITED TIME: {formatTime(timeLeft)} LEFT</span>
            <span className="animate-pulse">⚡</span>
          </div>
        </div>

        {/* Close button - smaller and less prominent */}
        <button
          onClick={handleClose}
          className="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors z-10 text-sm"
          aria-label="Close popup"
        >
          ✕
        </button>

        {/* Header with massive value proposition */}
        <div className="bg-gradient-to-br from-red-600 via-red-700 to-red-800 text-white p-8 text-center relative overflow-hidden">
          {/* Animated background elements */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-0 left-0 w-32 h-32 bg-yellow-400 rounded-full animate-pulse"></div>
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-green-400 rounded-full animate-pulse delay-1000"></div>
          </div>

          <div className="relative z-10">
            <div className="text-8xl mb-4 animate-bounce">🚨</div>
            <h3 className="text-3xl md:text-4xl font-black mb-4 leading-tight">
              {title}
            </h3>

            {/* Massive discount display */}
            <div className="bg-yellow-400 text-black px-8 py-4 rounded-2xl inline-block mb-4 transform rotate-3 shadow-2xl">
              <div className="text-6xl font-black">{discount}</div>
              <div className="text-xl font-bold">OFF TODAY ONLY!</div>
            </div>

            {/* Value stack */}
            <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-6 mb-4">
              <div className="text-2xl font-bold mb-2">🎁 EXCLUSIVE BONUSES INCLUDED:</div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-left">
                <div className="flex items-center">
                  <span className="text-green-400 mr-2">✅</span>
                  <span>Private Discord Access ($297 value)</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 mr-2">✅</span>
                  <span>1-on-1 Strategy Call ($497 value)</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 mr-2">✅</span>
                  <span>Trading Signals Bot ($197 value)</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 mr-2">✅</span>
                  <span>Lifetime Updates ($297 value)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Ultra Aggressive */}
        <div className="p-8 bg-gradient-to-br from-gray-50 to-white">
          {/* Social Proof Explosion */}
          <div className="text-center mb-8">
            <div className="bg-green-100 border-2 border-green-500 rounded-2xl p-6 mb-6">
              <div className="text-4xl mb-2">💰</div>
              <div className="text-2xl font-black text-green-800 mb-2">
                "                &quot;I Made $47,832 in 3 Months!&quot;"
              </div>
              <div className="text-green-700 font-semibold">
                - Sarah M., Alpha Group Member
              </div>
            </div>

            <p className="text-xl text-gray-800 mb-6 leading-relaxed font-semibold">
              {subtitle}
            </p>
          </div>

          {/* Scarcity Indicators */}
          <div className="bg-red-50 border-2 border-red-500 rounded-2xl p-6 mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-red-600 text-white px-4 py-2 rounded-full font-bold animate-pulse">
                ⚠️ ONLY 47 SPOTS LEFT TODAY
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="bg-white rounded-lg p-4 border-2 border-red-200">
                <div className="text-3xl font-black text-red-600">127</div>
                <div className="text-sm text-red-800">People viewing this offer</div>
              </div>
              <div className="bg-white rounded-lg p-4 border-2 border-red-200">
                <div className="text-3xl font-black text-red-600">23</div>
                <div className="text-sm text-red-800">Purchased in last hour</div>
              </div>
            </div>
          </div>

          {/* Massive Benefits List */}
          <div className="mb-8">
            <h4 className="text-2xl font-black text-center mb-6 text-gray-800">
              🎯 WHAT YOU GET RIGHT NOW:
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {[
                { text: 'Complete Crypto Mastery Course', value: '$997' },
                { text: 'Private VIP Discord Community', value: '$297' },
                { text: '1-on-1 Strategy Session', value: '$497' },
                { text: 'Daily Trading Signals', value: '$197' },
                { text: 'Portfolio Analysis Tool', value: '$297' },
                { text: 'Risk Management Calculator', value: '$197' },
                { text: 'Lifetime Course Updates', value: '$397' },
                { text: 'Mobile Trading App Access', value: '$197' }
              ].map((benefit, index) => (
                <div key={index} className="flex items-center justify-between bg-green-50 p-3 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <span className="text-green-600 mr-2 text-xl">✅</span>
                    <span className="font-semibold text-gray-800">{benefit.text}</span>
                  </div>
                  <span className="text-green-600 font-bold">{benefit.value}</span>
                </div>
              ))}
            </div>

            <div className="bg-yellow-400 text-black p-4 rounded-2xl mt-6 text-center">
              <div className="text-2xl font-black">TOTAL VALUE: $3,276</div>
              <div className="text-xl font-bold">YOUR PRICE TODAY: $97</div>
              <div className="text-lg font-semibold">YOU SAVE: $3,179 (97% OFF!)</div>
            </div>
          </div>

          {/* Ultra-Aggressive Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-gradient-to-r from-red-600 to-red-700 p-6 rounded-2xl text-white text-center">
              <div className="text-2xl font-black mb-4">
                ⏰ {formatTime(timeLeft)} TO CLAIM YOUR SPOT!
              </div>

              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email to claim 67% OFF + bonuses"
                className="w-full px-6 py-4 text-black text-lg font-semibold rounded-xl border-4 border-yellow-400 focus:ring-4 focus:ring-yellow-300 focus:border-yellow-300 mb-4"
                required
              />

              <Button
                type="submit"
                variant="primary"
                size="xl"
                loading={isSubmitting}
                className={`w-full text-2xl font-black py-6 ${getUrgencyBg()} hover:scale-105 transform transition-all shadow-2xl`}
              >
                {isSubmitting ? '🔄 PROCESSING...' : ctaText}
              </Button>
            </div>
          </form>

          {/* Final Push */}
          <div className="mt-8 text-center">
            <div className="bg-black text-white p-6 rounded-2xl mb-4">
              <div className="text-xl font-bold mb-2">🔒 100% RISK-FREE GUARANTEE</div>
              <div className="text-gray-300">
                Not satisfied? Get your money back within 30 days. No questions asked.
              </div>
            </div>

            <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                <span>SSL Secured</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Instant Access</span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                <span>30-Day Guarantee</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExitIntentPopup;

const { fontFamily } = require('tailwindcss/defaultTheme')

module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Enhanced brand colors
        'primary': '#F7D046',      // Gold - primary brand color
        'primary-dark': '#E5B618', // Darker gold for hover states
        'secondary': '#121212',    // Near-black for backgrounds
        'accent': '#3772FF',       // Blue accent for CTAs and highlights
        'success': '#00C48C',      // Green for success states
        'warning': '#FF9500',      // Orange for warnings
        'error': '#FF2D55',        // Red for errors

        // Legacy colors (preserved for compatibility)
        'blue': '#2655FF',
        'blue-primary': "#1E44CC",
        'black': '#081228',
        'green': '#00FF9E',
        'yellow': '#FCC229',
        'yellow-light': '#FFF9EA',
        'pink': '#EACFFF',
        'red': '#FC0019',
        'green-dark': '#00BF77',
        'green-light': '#D9FFF0',
        'gray': {
          50: '#fefefe',
          100: '#fdfdfd',
          200: '#e8eaed',
          300: '#f3f3f3',
          400: '#dbdbdb',
          500: '#c4c4c4',
          600: '#b6b6b6',
          700: '#AAAAAA',
          750: '#1A292C',
          800: '#6d6d6d',
          900: '#555555',
        },

        'linear': {
          0: '#0A093C00',
          100: '#0A093C',
        },
        'overlay': 'rgba(9, 23, 39, 0.53)',
        'translate': 'rgba(56, 56, 56, 0.58)',
        'disabled': 'rgba(255, 255, 255, 0.3)',
      },
      fontFamily: {
        manrope: ["var(--font-manrope)", ...fontFamily.serif],
        sans: ["var(--font-sans)", ...fontFamily.sans],
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #F7D046 0%, #E5B618 100%)',
        'gradient-dark': 'linear-gradient(135deg, #121212 0%, #1A1A1A 100%)',
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      animation: {
        'marquee': 'marquee 25s linear infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        marquee: {
          '0%': { transform: 'translateX(0%)' },
          '100%': { transform: 'translateX(-100%)' }
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        }
      },
      boxShadow: {
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 20px rgba(247, 208, 70, 0.3)',
      },
    },
    container: {
      padding: {
        DEFAULT: '1rem',
      }
    },
    fontSize: {
      'title': ['80px', {
        lineHeight: '100px',
        letterSpacing: '-0.02em',
      }],

      'h1': ['64px', {
        lineHeight: '72px',
        letterSpacing: '-0.015em',
      }],
      'h2': ['52px', {
        lineHeight: '64px',
        letterSpacing: '-0.015em',
      }],
      'h3': ['48px', {
        lineHeight: '52px',
        letterSpacing: '-0.015em',
      }],

      'headline': ['36px', {
        lineHeight: '130%',
        letterSpacing: '-0.015em',
      }],

      'b1': ['32px', {
        lineHeight: '130%',
        letterSpacing: '-0.01em',
      }],
      'b2': ['28px', {
        lineHeight: '130%',
        letterSpacing: '-0.005em',
      }],
      'b3': ['24px', {
        lineHeight: '130%',
        letterSpacing: '-0.005em',
      }],

      'callout': ['22px', {
        lineHeight: '130%',
        letterSpacing: '-0.005em',
      }],

      'sub1': ['20px', {
        lineHeight: '130%',
        letterSpacing: '-0.005em',
      }],
      'sub2': ['18px', {
        lineHeight: '130%',
        letterSpacing: '0em',
      }],
      'sub3': ['16px', {
        lineHeight: '130%',
        letterSpacing: '0em',
      }],
      'sub4': ['14px', {
        lineHeight: '150%',
        letterSpacing: '0em',
      }],

      'cap1': ['13px', {
        lineHeight: '130%',
        letterSpacing: '0em',
      }],
      'cap2': ['12px', {
        lineHeight: '130%',
        letterSpacing: '0em',
      }],
      'cap3': ['11px', {
        lineHeight: '100%',
        letterSpacing: '0em',
      }],
      'cap4': ['8px', {
        lineHeight: '100%',
        letterSpacing: '0em',
      }],
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('ps-scrollbar-tailwind'),
    function ({ addComponents }) {
      addComponents({
        '.container': {
          maxWidth: '100%',
          '@screen sm': {
            maxWidth: '100%',
          },
          '@screen md': {
            maxWidth: '100%',
          },
          '@screen lg': {
            maxWidth: '100%',
          },
          '@screen xl': {
            maxWidth: '100%',
          },
          '@screen 2xl': {
            maxWidth: '1440px',
          },
        }
      })
    }
  ],
}
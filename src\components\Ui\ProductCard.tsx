'use client';

import React from 'react';
import Image from 'next/image';
import Button from './Button';
import { cn } from '@/lib/cn';

interface ProductCardProps {
  product: {
    id: string;
    title: string;
    description?: string;
    image: string;
    price: number;
    originalPrice?: number;
    videoCount?: number;
    duration?: string;
    level?: string;
    rating?: number;
    reviewCount?: number;
    benefits: string[];
    type: 'course' | 'mentorship' | 'tool' | 'indicator';
    featured?: boolean;
    badge?: string;
  };
  className?: string;
  onEnroll?: (productId: string) => void;
  onPreview?: (productId: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  className, 
  onEnroll, 
  onPreview 
}) => {
  const handleEnrollClick = () => {
    if (onEnroll) {
      onEnroll(product.id);
    }
  };

  const handlePreviewClick = () => {
    if (onPreview) {
      onPreview(product.id);
    }
  };

  return (
    <div className={cn(
      "bg-white rounded-xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 flex flex-col h-full group",
      className
    )}>
      {/* Product Image with Overlay */}
      <div className="relative">
        <div className="relative w-full h-48">
          <Image 
            src={product.image} 
            alt={product.title} 
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
          <div className="p-4 w-full">
            <div className="flex justify-between items-center">
              {/* Price Badge */}
              <div className="flex items-center gap-2">
                <span className="bg-primary text-secondary px-3 py-1 rounded-full font-medium">
                  {product.price === 0 ? 'Free' : `$${product.price}`}
                </span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-white/70 line-through text-sm">
                    ${product.originalPrice}
                  </span>
                )}
              </div>
              
              {/* Video Count or Badge */}
              {product.videoCount && (
                <span className="text-white flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  {product.videoCount} videos
                </span>
              )}
              
              {product.badge && (
                <span className="bg-accent text-white px-2 py-1 rounded text-xs font-medium">
                  {product.badge}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Featured Badge */}
        {product.featured && (
          <div className="absolute top-4 left-4 bg-gradient-primary text-secondary px-3 py-1 rounded-full text-sm font-bold">
            FEATURED
          </div>
        )}

        {/* Preview Button */}
        {onPreview && (
          <button 
            onClick={handlePreviewClick}
            className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          >
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-4 hover:bg-white/30 transition-colors">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            </div>
          </button>
        )}
      </div>
      
      {/* Product Content */}
      <div className="p-6 flex-grow flex flex-col">
        <div className="mb-4">
          <h3 className="text-xl font-bold mb-2 line-clamp-2">{product.title}</h3>
          
          {/* Meta Information */}
          <div className="flex items-center text-gray-500 mb-4 flex-wrap gap-4">
            {product.rating && (
              <span className="flex items-center">
                <div className="flex mr-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg 
                      key={star} 
                      className={`w-4 h-4 ${star <= (product.rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`} 
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                {product.reviewCount && `(${product.reviewCount})`}
              </span>
            )}
            
            {product.duration && (
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                {product.duration}
              </span>
            )}
            
            {product.level && (
              <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
                {product.level}
              </span>
            )}
          </div>
        </div>
        
        {/* Benefits List */}
        <ul className="mb-6 text-gray-600 flex-grow space-y-2">
          {product.benefits.slice(0, 3).map((benefit, i) => (
            <li key={i} className="flex items-start">
              <svg className="w-5 h-5 text-primary mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-sm">{benefit}</span>
            </li>
          ))}
          {product.benefits.length > 3 && (
            <li className="text-sm text-gray-500 ml-7">
              +{product.benefits.length - 3} more benefits
            </li>
          )}
        </ul>
        
        {/* CTA Button */}
        <Button 
          variant="primary"
          size="md"
          onClick={handleEnrollClick}
          className="w-full"
        >
          {product.type === 'course' ? 'Enroll Now' : 
           product.type === 'mentorship' ? 'Book Session' :
           'Get Access'}
        </Button>
      </div>
    </div>
  );
};

export default ProductCard;
